<template>
    <div :class="['home', { active: store.showDown }]">
        <HomeHeader />
        <div ref="homeScroll" class="home-scroll" @scroll="handleScroll">
            <Banner />
            <!-- <Lamp /> -->
            <Jackpot v-if="showJackpot" />
            <div class="home-content">
                <KeepAlive>
                    <component :is="store.homeShow ? HomeMain : HomeTab" :type="type"></component>
                </KeepAlive>
            </div>
            <BaseFooter />
        </div>
        <Menu />
        <div :class="['home-float', { 'scroll-hide': !store.showBuoy }]" v-show="store.homeShow">
            <LoseReturn />
            <FirstRechargeIcon />
            <SignIcon />
            <SpinIcon />
            <InviteWheelIcon />
        </div>
        <RedeemDialog v-model="dialogVisible.showRedeem" />
        <Language v-model="dialogVisible.showLang" @confirm="onConfirm" />
        <Record v-if="dialogVisible.showRecord" v-model="dialogVisible.showRecord" :type="3" />
        <AutoReplay v-if="dialogVisible.robit" v-model="dialogVisible.robit" />
        <CheckDialog />
        <LuckySpin v-model="showLuckySpin" />
    </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, hydrateOnIdle } from 'vue'
import HomeHeader from '@/components/newHome/homeHeader.vue'
import Jackpot from '@/components/jackpot/index.vue'
import BaseFooter from '@/components/BaseFooter.vue'
import Menu from '@/components/newHome/menu.vue'
import LoseReturn from '@/components/newHome/homeFloat.vue'
// import Lamp from '@/components/newHome/lamp.vue'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { useJackpot } from '@/hooks/useJackpot'
import Banner from '@/components/newHome/banner.vue'
import 'swiper/css'
import * as service from '@/api/home'
import HomeMain from '@/components/newHome/home.vue'
import HomeTab from '@/components/newHome/homeTab.vue'
import { getRandomPeople } from '@/utils'
import eventBus from '@/utils/bus'

import Record from '@/components/wallet/record.vue'
import bus, { EVENT_KEY } from '@/utils/bus'
import { useRoute } from 'vue-router'
import Language from '@/components/newHome/language.vue'
import RedeemDialog from '@/components/dialog/redeemDialog.vue'
import { setlanguage } from '@/api/language'
import { useI18n } from 'vue-i18n'
import { useScroll } from '@/hooks/useScroll'
import { setLocaleLang } from '@/language'
import AutoReplay from '@/components/newHome/auto.vue'
import { Log } from '@/api/log'
import FirstRechargeIcon from '@/components/newHome/firstRechargeIcon.vue'
import SignIcon from '@/components/newHome/signIcon.vue'
import SpinIcon from '@/components/newHome/spinIcon.vue'
import InviteWheelIcon from '@/components/newHome/InviteWheelIcon.vue'

const CheckDialog = defineAsyncComponent({
    loader: () => import('@/components/dialog/checkDialog.vue'),
    hydrate: hydrateOnIdle(/* 传递可选的最大超时 */),
})

const LuckySpin = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/views/luckyspin/index.vue'),
    delay: 2000,
})

const store = useBaseStore()
const jackpotStore = useJackpotStore()
const route = useRoute()
const i18n = useI18n()
const { handleScroll } = useScroll()
const { showJackpot, fetchJackpotData } = useJackpot()

const showLuckySpin = ref(false)

const type = ref('')
const homeScroll = ref(null)

const refreshIcon = ref(false)
let init = false

const dialogVisible = reactive({
    showRedeem: false,
    showLang: false,
    showInivte: false,
    showBind: false,
    showRecord: false,
    robit: false,
})

// 获取Jackpot数据
const fetchJackpotDataFn = async () => {
    try {
        await fetchJackpotData(false, false)
    } catch (error) {}
}

watch(
    () => store.wallet?.currency,
    () => {
        if (route.path === '/') {
            getList(!store.tabData.length)
            getVipInfo(false)
						fetchJackpotDataFn()
        }
    }
)

// 监听登录状态变化，当token变化时重新获取Jackpot数据
watch(
    () => store.token,
    () => {
        fetchJackpotDataFn()
    }
)

watch(
    () => store.homeShow,
    () => {
        if (store.homeShow && type.value !== 'history') {
            getList(!store.tabData.length)
        }
        homeScroll.value.scrollTop = 0
    }
)

const getList = async (loading = true) => {
    try {
        const listRes = await service.getHomeList(loading)

        if (listRes.code === 200) {
            Object.keys(listRes.data.gameStat).map((key) => {
                const gameConfig = listRes.data.gameStat[key]

                gameConfig.gameonline = getRandomPeople(gameConfig.gameonline)
            })
            store.setUserInfo({
                ...store.userInfo,
                isReseller: listRes.data?.isReseller || false,
            })

            store.setHomeData(listRes.data)
        }
        init = true
    } catch (e) {
        console.log(e)
    }
}
const handleTabView = (item) => {
    store.homeShow = item.flag
    type.value = item.type
}
const logOut = () => {
    getList()
}

const handleMenuSet = (param) => {
    if (['/bindPhone', '/bindInviter'].includes(param)) {
        store.showMenu = false
        eventBus.emit('activity', {
            param,
        })
        return
    }
    store.showMenu = false
    dialogVisible[param] = true
}
const onConfirm = (lang) => {
    if (store.token) {
        setlanguage(lang).then((res) => {
            if (res.code === 200) {
                store.lang = lang
                setLocaleLang(lang)

                showSuccessToast(i18n.t('Set_language_success'))
            } else {
                showFailToast(i18n.t('Set_language_fail'))
            }
        })
    } else {
        store.lang = lang
        setLocaleLang(lang)
        showSuccessToast(i18n.t('Set_language_success'))
    }
}
const getVipInfo = async (loading = true, tip = true) => {
    try {
        const info = await store.getVipInfo(loading, tip)
        if (info.code === 200) {
            store.setvipInfo(info)
        }
    } catch (e) {
        console.log(e)
    }
}

onBeforeMount(() => {
    getList(!store.tabData.length)
    fetchJackpotDataFn()
    Log({
        event: 'enter_home',
    })
    eventBus.on('handleTabView', handleTabView)
    bus.on(EVENT_KEY.LOGOUT, logOut)
    bus.on('menuSet', handleMenuSet)
    bus.on('updateVip', getVipInfo)
    bus.on('showLuckySpin', (flag: boolean) => {
        showLuckySpin.value = flag
    })
})
onBeforeUnmount(() => {
    eventBus.off('handleTabView', handleTabView)
    bus.off(EVENT_KEY.LOGOUT, logOut)
    bus.off('menuSet', handleMenuSet)
    bus.off('updateVip', getVipInfo)
    bus.off('showLuckySpin', (flag: boolean) => {
        showLuckySpin.value = flag
    })
})

onActivated(() => {
    init && getList(!store.tabData.length)
    getVipInfo(false, false)
    eventBus.emit('freeGametimes')
})
</script>
<style lang="scss" scoped>
$padTop: 5px;
$tabH: 120px;
.home {
    &.active {
        --home-header: calc(196px + constant(safe-area-inset-top));
        --home-header: calc(196px + env(safe-area-inset-top));
    }
    top: 0px;
    height: calc(var(--vh, 1vh) * 100);
    padding-top: $padTop;

    .home-scroll {
        height: calc(var(--vh, 1vh) * 100 - var(--home-header) - var(--footer-height));
        overflow-y: scroll;
    }

    // .home-content {
    //     // padding-left: 29px;
    //     // height: calc(var(--vh, 1vh) * 100 - var(--home-header) - var(--footer-height));
    //     // overflow-y: scroll;
    //     // - var(--banner-height) - var(--lamp-height)
    // }
    :deep(.van-tabs) {
        height: 100%;

        .van-tabs__wrap {
            height: $tabH;
            padding: 20px 0;
        }
        .van-tabs__nav {
            background: var(--main-bg);

            .van-tab {
                margin-right: 6px;
                width: 110px;
                height: 80px;
                padding: 0;
                background-color: #2a2d3d;
                border-radius: 14px;
                text-align: center;
            }

            .van-tabs__line {
                display: none;
            }
            .van-tab__text {
                display: flex;
                align-items: center;
            }
            .tab-title {
                @apply flex flex-col items-center justify-center;
                .tab-title_icon {
                    margin: 0 auto;
                    width: 46px;
                    height: 40px;
                    object-fit: cover;
                    overflow: hidden;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
                .tab-title_name {
                    position: relative;
                    top: 4px;
                    font-family: MicrosoftYaHei;
                    font-size: 20px;
                    font-weight: bold;
                    color: #668292;
                    line-height: 28px;
                }
            }
            .van-tab--active {
                background-color: #4b5366;

                .tab-title_name {
                    color: #fff;
                }
            }
        }
    }
    .home-float {
        width: 200px;
        height: 1px;
        position: fixed;
        right: 0;
        bottom: calc(var(--footer-height) + 20px);
        transition: all 0.3s ease;
        transform: translate3d(0, 0, 0);

        .home-server {
            position: absolute;
            right: 20px;
            bottom: 10px;
        }
    }
    .scroll-hide {
        transform: translate3d(100%, 0, 0);
    }
}
</style>
