<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-25 15:14:02
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 18:17:47
 * @FilePath     : /src/components/jackpot/RankingList.vue
 * @Description  : 通用列表组件 - 可复用的表格展示组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-25 15:14:02
-->

<template>
    <div class="ranking-list-container">
        <!-- Loading 状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="dataList.length === 0" class="empty-container">
            <div class="empty-content">
                <img src="@/assets/img/jackpot/empty.png" alt="" class="empty-image" />
                <div class="empty-text">{{ $t('jackpot.empty.message') }}</div>
            </div>
        </div>

        <!-- 正常数据显示 -->
        <template v-else>
            <!-- 表格头部 -->
            <div class="table-header">
                <div class="header-cell col-1-header">{{ headers.col1 }}</div>
                <div class="header-cell col-2-header">{{ headers.col2 }}</div>
                <div class="header-cell col-3-header">{{ headers.col3 }}</div>
                <div class="header-cell col-4-header">{{ headers.col4 }}</div>
            </div>

            <!-- 数据列表 -->
            <div class="data-list">
                <div v-for="(item, index) in dataList" :key="index" class="list-item" :style="{ backgroundColor: getRowBackgroundColor(index) }">
                    <div class="body-cell col-1-cell">
                        <img v-if="shouldShowAvatar(item.col1)" :src="getAvatarSrc(item.col1)" :alt="`${item.col1} place`" class="avatar-img" />
                        <span v-else class="col-1-text">{{ formatRankDisplay(item.col1) }}</span>
                    </div>
                    <div class="body-cell col-2-cell">
                        <span>{{ item.col2 }}</span>
                    </div>
                    <div class="body-cell col-3-cell">
                        <span v-if="displayMode === 'rewards'" class="col-3-amount-rewards">
                            {{ curSymbol[wallet?.currency] }}{{ formatNumber(item.col3) }}
                            <!-- <span v-if="showRate" class="col-3-percent">({{ item.percent }})</span> -->
                        </span>
                        <span v-else>{{ curSymbol[wallet?.currency] }}{{ formatNumber(item.col3) }}</span>
                    </div>
                    <div class="body-cell col-4-cell">
                        <span v-if="displayMode === 'rewards'" class="col-4-date">{{ item.col4 }}</span>
                        <template v-else>
                            <span class="col-4-amount">{{ curSymbol[wallet?.currency] }}{{ formatNumber(item.col4) }}</span>
                            <span v-if="showRate" class="col-4-rate">({{ item.rate }}%)</span>
                        </template>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'

const store = useBaseStore()
const { curSymbol, wallet } = storeToRefs(store)

const props = defineProps({
    dataList: {
        type: Array,
        default: () => [],
    },
    // 表头文案配置
    headers: {
        type: Object,
        default: () => ({
            col1: 'Column 1',
            col2: 'Column 2',
            col3: 'Column 3',
            col4: 'Column 4',
        }),
    },
    showRate: {
        type: Boolean,
        default: true,
    },
    numberFormatter: {
        type: Function,
        default: null,
    },
    // 斑马线颜色配置 - true表示奇数行用深色，false表示偶数行用深色
    zebraStripePattern: {
        type: Boolean,
        default: true,
    },
    // 显示模式
    displayMode: {
        type: String,
        default: 'history',
    },
    // 加载状态
    loading: {
        type: Boolean,
        default: false,
    },
})

// 内置头像数据 - 前3名的头像
const avatarData = {
    1: 'data:image/png;base64,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',
    2: 'data:image/png;base64,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',
    3: 'data:image/png;base64,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',
}

// 判断是否应该显示头像（前3名）
const shouldShowAvatar = (rank) => {
    return rank >= 1 && rank <= 3
}

// 获取头像链接
const getAvatarSrc = (rank) => {
    return avatarData[rank] || ''
}

// 格式化排名显示
const formatRankDisplay = (rank) => {
    if (typeof rank === 'number' && rank > 3) {
        return `${rank}th`
    }
    return rank
}

// 格式化数字 - 内置默认格式化逻辑
const formatNumber = (num) => {
    // 默认格式化：直接转换为字符串，不添加千分位分隔符
    return num.toString()
}

// 获取行背景色
const getRowBackgroundColor = (index) => {
    const isOdd = index % 2 === 0 // 注意：数组索引从0开始，所以0是"第一行"(奇数行)

    if (props.zebraStripePattern) {
        // 默认模式：奇数行深色，偶数行浅色
        return isOdd ? '#333738' : '#292d2e'
    } else {
        // 反转模式：奇数行浅色，偶数行深色
        return isOdd ? '#292d2e' : '#333738'
    }
}
</script>

<style lang="scss" scoped>
$bg-primary: #2a3132;
$bg-hover: #4a5152;
$text-secondary: #b3bec1;

.ranking-list-container {
    flex: 1;
    background-color: #292d2e;
    /* 确保列表内容在iPhone上有足够的底部间距 */
    padding-bottom: calc(20px + env(safe-area-inset-bottom, 0px));

    .table-header {
        display: flex;
        padding: 16px 0;
        background: #292d2e;
        font-size: 20.9341px;
        line-height: 27.9122px;

        .header-cell {
            color: $text-secondary;
            font-weight: 500;
            text-align: center;

            &.col-1-header {
                width: 97.6927px;
            }
            &.col-2-header,
            &.col-3-header,
            &.col-4-header {
                flex: 1;
            }
        }
    }

    .data-list {
        font-size: 20.9341px;
        line-height: 27.9122px;

        .list-item {
            display: flex;
            align-items: center;
            transition: background-color 0.2s;

            .body-cell {
                padding: 20.9341px 13.9561px;
            }

            .col-1-cell {
                width: 97.6927px;
                display: flex;
                align-items: center;
                justify-content: center;

                .avatar-img {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    object-fit: cover;
                }

                .col-1-text {
                    color: $text-secondary;
                    font-weight: 500;
                }
            }

            .col-2-cell {
                flex: 1;
                text-align: center;
                color: rgb(179, 190, 193, 1);
            }

            .col-3-cell {
                flex: 1;
                color: rgb(36, 238, 137, 1);
                text-align: center;
            }

            .col-4-cell {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 4px;

                .col-4-amount {
                    color: rgb(36, 238, 137, 1);
                }

                .col-4-rate {
                    color: $text-secondary;
                }

                .col-4-date {
                    color: #ffffff;
                }
            }
        }
    }

    // MyRewards 模式下的特殊样式
    .col-3-amount-rewards {
        color: rgb(36, 238, 137, 1);

        .col-3-percent {
            color: #ffffff;
        }
    }

    // Loading 状态样式
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60px 20px 60px;
        min-height: 300px;
        // background: rgb(43, 43, 43);

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(36, 238, 137, 0.2);
            border-top: 4px solid rgb(36, 238, 137);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    }

    // 空状态样式
    .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60px 20px;
        min-height: 200px;

        .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            .empty-image {
                width: 334.9464px;
                max-width: 100%;
                height: auto;
            }

            .empty-text {
                margin-top: 28px;
                font-size: 24.4232px;
                color: #fff;
                line-height: 34.8902px;
                text-align: center;
            }
        }
    }
}

// 旋转动画
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
