<template>
    <!-- 自定义导航栏 -->
    <div class="pop-title">
        <button class="nav-back-button" type="button" @click="handleBack">
            <svg class="back-icon" aria-hidden="true">
                <use href="#icon-svg-JiantouL" fill=""></use>
            </svg>
        </button>
        <span class="title">{{ $t('jackpot.title') }}</span>
        <div v-if="loggedIn" class="nav-actions">
            <button class="rewards-button" type="button" @click="$emit('openMyRewards')">
                <span class="rewards-text">{{ $t('jackpot.navigation.my_rewards') }}</span>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { nextTick, computed } from 'vue'
import eventBus, { EVENT_KEY } from '@/utils/bus'
import { isLoggedIn } from '@/utils'

const router = useRouter()

const loggedIn = computed(() => isLoggedIn())

defineEmits<{
    close: []
    openMyRewards: []
}>()

const handleBack = async () => {
    eventBus.emit(EVENT_KEY.JACKPOT_VIEW_EXIT)

    // 使用 nextTick 确保事件处理完成后再执行路由返回
    await nextTick()
    router.back()
}
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$bg-secondary: #383838;
$bg-hover: #4a4a4a;
$bg-button: #464f50;
$text-primary: #ffffff;
$accent-color: #4caf50;

.pop-title {
    position: sticky;
    top: env(safe-area-inset-top);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 27.9122px;
    font-weight: 800;
    line-height: 55.8244px;
    height: 97.6927px;
    min-height: 97.6927px;
    background-color: rgb(50, 55, 56, 1);
    z-index: 100;
    /* 确保在iPhone上不被状态栏遮挡 */
    padding-top: env(safe-area-inset-top);

    .nav-back-button {
        width: 55.8244px;
        height: 55.8244px;
        border-radius: 10.4671px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: transparent;
        position: absolute;
        left: 27.9122px;
        /* 调整top位置，考虑安全区域 */
        top: calc(20.9341px + env(safe-area-inset-top));
        right: auto;
        background-color: $bg-button;

        .back-icon {
            width: 31.4012px;
            height: 31.4012px;
            fill: rgb(179, 190, 193, 1);
        }
    }

    .title {
        font-size: 27.9122px;
        color: $text-primary;
        position: absolute;
        left: 50%;
        /* 调整垂直位置，考虑安全区域 */
        top: calc(50% + env(safe-area-inset-top) / 2);
        transform: translate(-50%, -50%);
        white-space: nowrap;
    }

    .nav-actions {
        position: absolute;
        right: 6.978px;
        margin-left: auto;
        /* 调整高度和对齐，考虑安全区域 */
        height: calc(100% - env(safe-area-inset-top));
        top: env(safe-area-inset-top);
        display: flex;
        align-items: center;

        .rewards-button {
            background: $bg-button;
            border-radius: 10.4671px;
            height: 55.8244px;
            margin-right: 20.9341px;
            display: flex;
            align-items: center;
            justify-content: center;

            .rewards-text {
                font-size: 25.121px;
                color: #fff;
                margin: 0 13.9561px;
            }
        }
    }
}
</style>
