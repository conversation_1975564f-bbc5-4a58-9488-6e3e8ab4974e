<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-08 17:49:14
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-25 15:25:54
 * @FilePath     : /src/components/jackpot/MorePage.vue
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-08 17:49:14
-->
<template>
    <div class="more-page">
        <JackpotHeader @close="handleClose" @open-my-rewards="openMyRewards" />

        <!-- 中间内容区域 -->
        <JackpotContent @close="handleClose" @open-rules="openRules" @open-history-popup="openHistoryPopup" />

        <!-- 通用弹窗 -->
        <van-popup
            v-model:show="showPopup"
            position="bottom"
            :style="{ height: '90%', width: '100%', background: '#292d2e' }"
            :overlay="true"
            :close-on-click-overlay="true"
            :duration="0.15"
            round
            teleport="body"
        >
            <History
                v-if="popupType === 'history'"
                :initial-date-index="historyDateIndex"
                @close="closePopup"
                @date-change="updateHistoryDateIndex"
            />
            <MyRewards v-else-if="popupType === 'rewards'" @close="closePopup" />
            <Rules v-else-if="popupType === 'rules'" @close="closePopup" />
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated } from 'vue'
import { useJackpot } from '@/hooks/useJackpot'
import { useRoute } from 'vue-router'
import History from './History.vue'
import MyRewards from './MyRewards.vue'
import Rules from './Rules.vue'
import JackpotHeader from './JackpotHeader.vue'
import JackpotContent from './JackpotContent.vue'

import eventBus, { EVENT_KEY } from '@/utils/bus'

const emit = defineEmits(['close'])

const route = useRoute()
const { fetchJackpotData } = useJackpot()

// 获取最新的Jackpot数据
const fetchLatestJackpotData = async () => {
    try {
        await fetchJackpotData(false, false)
    } catch (error) {
        console.error('获取Jackpot数据失败:', error)
    }
}

// 弹窗状态
const showPopup = ref(false)
const popupType = ref('') // 'history'、'rewards' 或 'rules'

// 记住历史页面的日期索引
const historyDateIndex = ref(1)

const openHistoryPopup = () => {
    popupType.value = 'history'
    showPopup.value = true
}

const openMyRewards = () => {
    popupType.value = 'rewards'
    showPopup.value = true
}

const openRules = () => {
    popupType.value = 'rules'
    showPopup.value = true
}

// 更新历史日期索引
const updateHistoryDateIndex = (newIndex) => {
    historyDateIndex.value = newIndex
}

const closePopup = () => {
    showPopup.value = false
    popupType.value = ''
}

const handleClose = () => {
    emit('close')
}

onMounted(() => {
    fetchLatestJackpotData()
})

onActivated(() => {
    fetchLatestJackpotData()
    eventBus.emit(EVENT_KEY.JACKPOT_VIEW_ENTER)
})
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$bg-secondary: #383838;
$bg-hover: #4a4a4a;
$bg-button: #464f50;
$text-primary: #ffffff;
$accent-color: #4caf50;

.more-page {
    background: $bg-primary;
    display: flex;
    flex-direction: column;
    color: $text-primary;
    /* 移除margin-top，因为JackpotHeader已经处理了安全区域 */
    height: 100vh;
    overflow: auto;
    /* 确保内容可以滚动到底部，避开iPhone底部安全区域 */
    padding-bottom: env(safe-area-inset-bottom);
}
</style>
