<template>
    <!-- 新增的组件区域 -->
    <div class="jackpot-components">
        <div class="cards-section">
            <svg class="rule-icon" aria-hidden="true" @click="$emit('openRules')">
                <use href="#icon-svg-RuleIcon"></use>
            </svg>
            <JackpotPrize />
            <TimeChampion />
        </div>
        <VipCard v-if="loggedIn" :show-go-button="true" @close="$emit('close')" />
    </div>
    <!-- 主要内容容器 -->
    <div class="list-container">
        <!-- 头部状态栏 -->
        <div class="header-status">
            <div class="status-indicator">
                <svg class="background-icon" aria-hidden="true">
                    <use href="#icon-svg-JackpotJiao"></use>
                </svg>
                <svg class="spinning-icon" aria-hidden="true">
                    <use href="#icon-svg-JackpotYuan"></use>
                </svg>
                <div class="status-text">{{ $t('jackpot.status.active') }}</div>
            </div>

            <div class="date">{{ formatDateOnly(jackpotData?.time, '/') }}</div>
            <button class="history-btn" @click="$emit('openHistoryPopup')">
                <span>{{ $t('jackpot.navigation.history') }}</span>
                <span class="arrow">›</span>
            </button>
        </div>

        <div class="page-content">
            <RankingList :data-list="displayList" :headers="tableHeaders" :zebra-stripe-pattern="true" :loading="isLoading" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { watchEffect, ref, computed } from 'vue'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import RankingList from './RankingList.vue'
import TimeChampion from './TimeChampion.vue'
import VipCard from './VipCard.vue'
import JackpotPrize from './JackpotPrize.vue'
import { processPlayerList } from './calculator'
import { formatDateOnly } from '@/utils/date'
import { isLoggedIn } from '@/utils'
import type { ProcessedPlayerData } from './types'
import { useI18n } from 'vue-i18n'

defineEmits<{
    close: []
    openRules: []
    openHistoryPopup: []
}>()

const jackpotStore = useJackpotStore()
const { jackpotData } = storeToRefs(jackpotStore)

const { t } = useI18n()

const loggedIn = computed(() => isLoggedIn())

const tableHeaders = {
    col1: t('jackpot.table.rank'),
    col2: t('jackpot.table.player'),
    col3: t('jackpot.table.turnover'),
    col4: t('jackpot.table.prize'),
}

const displayList = ref<ProcessedPlayerData[]>([])

const isLoading = ref(true)

// 使用响应式计算来自动更新显示数据
watchEffect(() => {
    if (jackpotData.value && jackpotData.value.pool && jackpotData.value.rankReward && jackpotData.value.rank) {
        displayList.value = processPlayerList(jackpotData.value.rank, jackpotData.value.rankReward, jackpotData.value.pool)
        isLoading.value = false
    } else {
        displayList.value = []
        isLoading.value = false
    }
})
</script>

<style lang="scss" scoped>
$bg-primary: #2b2b2b;
$bg-secondary: #383838;
$bg-hover: #4a4a4a;
$bg-button: #464f50;
$text-primary: #ffffff;
$accent-color: #4caf50;

.list-container {
    margin: 30px;
    margin-top: 10px;
    /* 增加底部边距以避开iPhone底部安全区域和工具栏 */
    margin-bottom: calc(50px + env(safe-area-inset-bottom) + 20px);
    border-radius: 16px;
    background: $bg-secondary;
    // overflow: hidden;
}

.header-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 24.4232px;

    .status-indicator {
        position: relative;
        display: flex;
        align-items: center;
        width: 139.561px;
        height: 48.8464px;
        color: #000;
        font-size: 20.9341px;
        line-height: 48.8464px;

        .background-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 139.561px;
            height: 48.8464px;
            fill: rgb(36, 238, 137, 1);
        }

        .spinning-icon {
            position: relative;
            width: 27.9122px;
            fill: #000;
            z-index: 10;
            margin-left: 8px;
            animation: spin 5s linear infinite !important;
        }

        .status-text {
            position: relative;
            z-index: 10;
            font-weight: 800;
            margin-left: 6.978px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #000;
        }
    }

    .date {
        font-weight: 500;
        color: $text-primary;
    }

    .history-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        background: $bg-button;
        padding: 8px 20px;
        margin-right: 16px;
        border-radius: 10px;
        cursor: pointer;
        transition: background-color 0.2s;
        height: 55.8244px;

        span:nth-child(1) {
            margin: 0 10px;
        }

        .arrow {
            color: rgb(132, 145, 148, 1);
        }

        &:hover {
            background: #525254;
        }
    }
}

// 新增的组件区域样式
.jackpot-components {
    padding: 20px 30px;
    background: $bg-primary;
    /* 确保在iPhone上有足够的顶部间距 */
    padding-top: calc(20px + env(safe-area-inset-top, 0px));

    .cards-section {
        position: relative;
        > * {
            margin-bottom: 20px;
        }
        .rule-icon {
            width: 31.4012px;
            height: 31.4012px;
            position: absolute;
            top: 17.4451px;
            right: 17.4451px;
            fill: rgb(179, 190, 193, 1);
        }
    }
}

.page-content {
    flex: 1;
}

// 旋转动画
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
