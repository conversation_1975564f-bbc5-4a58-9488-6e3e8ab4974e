<template>
    <van-popup class="invite-pop" v-model:show="show" :overlay="false" :duration="0.2" @closed="onClosed">
        <div class="menu-title">
            <button class="menu-close" @click="onClosed"></button>
            <div class="nav-title">{{ $t('invite_wheel_title') }}</div>
            <button class="menu-help" @click="onClickHelp"></button> <button class="menu-record" @click="onClickRecord"></button>
        </div>
        <div class="invite-content">
            <div class="invite-state">
                <img src="@/assets/img/invitewheel/djs.png" />
                <div class="invite-state-title">{{ $t('invite_state_title') }}</div>
                <div class="invite-state-time">
                    <van-count-down :time="lasttime">
                        <template #default="timeData">
                            <span>{{ timeData.days }}</span
                            >D<span>:{{ String(timeData.hours).padStart(2, '0') }}</span
                            >H<span>:{{ String(timeData.minutes).padStart(2, '0') }}</span
                            >M<span>:{{ String(timeData.seconds).padStart(2, '0') }}</span
                            >S
                        </template>
                    </van-count-down>
                </div>
                <div class="invite-state-award">
                    <span class="number">{{ store.curSymbol[store.wallet?.currency] }}{{ rednum.toFixed(2) }}</span>
                </div>
                <div class="invite-state-btn">
                    <button @click="onCashOut">{{ $t('Cash Out') }}</button>
                </div>
            </div>

            <Turntable
                ref="turntable"
                class="invite-turntable"
                :prizes="prizes"
                :free-times="freetimes"
                @invitewheel:rotate-end="handleRotateEnd"
                @invitewheel:rotate-start="handleRotateStart"
            />
            <div class="invite-share">
                <button @click="onClickShare">
                    <img src="@/assets/img/invitewheel/invite.png" /><span>{{ $t('invite_wheel_share_btn_txt') }}</span>
                </button>
                <div
                    class="invite_wheel_reward_desc"
                    v-html="
                        $t('invite_wheel_reward_desc', {
                            remain: store.curSymbol[store.wallet?.currency] + ' ' + lastReward,
                            total: store.curSymbol[store.wallet?.currency] + ' ' + userinfo?.needRealReward?.toFixed(2),
                        })
                    "
                ></div>
                <div class="invite_wheel_record">
                    <div class="invite_wheel_record_bg"></div>

                    <div class="invite_wheel_record_content">
                        <div class="invite_wheel_record_title">{{ $t('Record') }}</div>
                        <div class="invite_wheel_record-card" v-for="(item, index) in records" :key="index" :class="{ disabled: false }">
                            <div class="record-time">{{ dayjs(JSON.parse(item).time).format('YYYY-MM-DD HH:mm:ss') }}</div>
                            <div class="record-gold">{{ store.curSymbol[store.wallet?.currency] + JSON.parse(item).addred.toFixed(3) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </van-popup>
    <BTip v-model="showtip" :title="$t('Invite Wheel')" :content="$t('invite_wheel_help_desc')" />
    <InviteAwardRecord v-model="recordshow" />
    <InvitecashoutPop v-model="showcashout" @invitewheel:showshare="onClickShare" />
    <ShareDialog v-model="showShare" :social="['facebook', 'telegram', 'whatsapp', 'link']" :config="{ from: 'redfission' }" z-index="3002" />
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import PopupManager from '@/utils/PopupManager'
import Turntable from './invitespin.vue'
import dayjs from 'dayjs'
import BTip from '@/components/wallet/btip.vue'
import InviteAwardRecord from './inviteAwardRecord.vue'
import ShareDialog from '../dialog/ComShareDialog.vue'
import { RedfissionConfig, RedfissionUser } from '@/stores/types'
import InvitecashoutPop from './invitecashoutPop.vue'
// import InviteAwardPop from './inviteAwardPop.vue'

const store = useBaseStore()
const turntable = ref(null)
const showtip = ref(false)
const showShare = ref(false)
const configs = ref<RedfissionConfig>()
const prizes = ref([])
const userinfo = ref<RedfissionUser>()
const freetimes = ref(0)
const rednum = ref(0)
const lastReward = ref('0.00')
const records = ref([])

let animationFrameId: number | null = null

const show = ref(true)
const recordshow = ref(false)
const showcashout = ref(false)

const lasttime = computed(() => {
    const endTime = store.redfissionInfo?.user?.endTime
    const now = new Date().getTime()
    if (endTime) {
        const end = new Date(endTime).getTime()
        const distance = end - now
        console.log('distance', distance, end)
        return distance
    }
    return 0
})

const onClosed = () => {
    PopupManager.closeCurrentPopup()
}

const onClickHelp = () => {
    showtip.value = true
}

const onClickRecord = () => {
    recordshow.value = true
}

const onCashOut = () => {
    showcashout.value = true
}

const onClickShare = () => {
    showShare.value = true
}

const handleRotateStart = function () {
    console.log('抽奖开始')
    startfreespin()
}

const handleRotateEnd = function () {
    console.log('抽奖结束，中奖索引:', userinfo.value)
    freetimes.value = userinfo.value.freespin
    // rednum.value = userinfo.value.rednum
    startAnimation(userinfo.value.rednum)
    lastReward.value = (userinfo.value.needRealReward - userinfo.value.rednum).toFixed(2)
    getRecords()
}

const startfreespin = async (isloading: boolean = false) => {
    try {
        const res = await store.actionEvent(
            {
                id: 'redfission',
                method: 'freespin',
            },
            false,
            isloading
        )

        if (res.code === 200) {
            console.log('redfission freespin', res)
            store.redfissionInfo.user = res.user
            userinfo.value = res.user
            if (turntable.value) {
                turntable.value.startRotate(Math.max(0, res.idpos - 1))
                return
            }
        }
    } catch (e) {
        console.log(e)
    }
    onClickShare()
}

const getRecords = async (isloading: boolean = false) => {
    try {
        const res = await store.actionEvent(
            {
                id: 'redfission',
                method: 'freespinlogs',
            },
            false,
            isloading
        )

        if (res.code === 200) {
            console.log('redfission openred', res)
            records.value = res.list
        }
    } catch (e) {
        console.log(e)
    }
}

const loadSpinData = () => {
    const redfissionInfo = store.redfissionInfo
    if (redfissionInfo.status !== 1) {
        // PopupManager.checkQueue()
        PopupManager.closeCurrentPopup()
    }
    userinfo.value = redfissionInfo.user
    configs.value = redfissionInfo.Configs
    prizes.value = configs.value.freespinconfig
    rednum.value = userinfo.value.rednum
    freetimes.value = userinfo.value.freespin
    lastReward.value = (userinfo.value.needRealReward - userinfo.value.rednum).toFixed(2)
    show.value = true
}

const startAnimation = (endNumber: number, duration: number = 1000) => {
    const startTime = performance.now()
    const endTime = startTime + duration
    const startNumber = rednum.value

    const animate = (currentTime: number) => {
        if (currentTime >= endTime) {
            rednum.value = endNumber
            return
        }

        const progress = (currentTime - startTime) / duration
        // 使用缓动函数使动画更自然
        const easedProgress = easeOutQuad(progress)
        rednum.value = startNumber + (endNumber - startNumber) * easedProgress

        animationFrameId = requestAnimationFrame(animate)
    }

    animationFrameId = requestAnimationFrame(animate)
}

// 缓动函数
const easeOutQuad = (t: number): number => {
    return t * (2 - t)
}

onMounted(() => {
    loadSpinData()
    getRecords()
})

onBeforeUnmount(() => {
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
    }
})
</script>
<style lang="scss" scoped>
.invite-pop {
    width: 100%;
    height: 100vh;
    max-width: 100%;
    background-color: #202222;
    overflow-y: hidden;
}

.invite-content {
    padding: 5px 26px;
    height: calc(var(--vh, 1vh) * 100 - max(var(--menu-header), 96px));
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
    align-items: center;

    .invite-share {
        position: relative;
        width: 100%;
        display: flex;
        justify-content: center;
        text-align: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        margin-top: 100px;
        gap: 10px;
        span {
            font-size: 28px;
            font-weight: 500;
            line-height: 28px;
        }

        .invite_wheel_reward_desc {
            font-size: 24px;
            color: #fff;
            font-weight: 400;
            line-height: 24px;
            margin-top: 20px;
            :deep(.text-yellow) {
                color: #f5c518;
            }
        }

        button {
            width: 100%;
            height: 75px;
            border-radius: 99px;
            background: linear-gradient(90deg, #2dee88, #96e974);
            color: #000;
            font-weight: bold;
            font-size: 20px;
            box-sizing: border-box;
            padding: 10px 0;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        img {
            display: inline-block;
            width: 46px;
            height: 50px;
        }
    }

    .invite_wheel_record {
        width: 100%;
        height: 800px;
        margin-top: 15px;
        border-radius: 30px;
        position: relative;
        box-sizing: border-box;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .invite_wheel_record_bg {
            width: 100%;
            height: 40px;
            border-radius: 99px;
            border: #4e514f 6px solid;
        }

        .invite_wheel_record_content {
            height: 100%;
            max-height: 800px;
            overflow-y: auto;
            width: 96%;
            margin-top: -20px;
            color: #fff;
            font-size: 26px;
            font-weight: 400;
            padding: 20px;
            background: linear-gradient(180deg, #3f3c3f 0%, #2a2c2b 13%, #2a2c2b 100%);
            .invite_wheel_record_title {
                width: 100%;
                height: 60px;
            }
            .invite_wheel_record-card {
                width: 100%;
                display: flex;
                align-items: center;
                flex-direction: row;
                justify-content: space-between;
                padding: 20px 10px 20px 10px;
                border-top: 0.5px solid #434345;

                .record-gold {
                    font-size: 34px;
                    color: #25d276;
                    font-weight: 800;
                }
            }
        }
    }
}
.menu-title {
    @apply flex justify-center relative;
    width: 100%;
    height: var(--menu-header);
    z-index: 1000;
    background-color: rgba(50, 55, 56, 1);
    color: #fff;
    display: flex;
    text-align: center;
    align-items: flex-end;
    padding-bottom: 30px;

    button {
        width: 55px;
        height: 55px;
        position: absolute;
    }

    button::after {
        content: '';
        position: absolute;
        top: -15px;
        left: -15px;
        right: -15px;
        bottom: -15px;
    }

    .menu-close {
        left: 25px;
        border-radius: 10px;
        background: url('@/assets/img/icon/rounded-left.svg') no-repeat center center;
        background-size: 70% 70%;
        background-color: rgb(70, 79, 80, 0.5);
        color: #fff;
    }
    .menu-help {
        right: 110px;
        background: url('@/assets/img/icon/question-circle-min.svg') no-repeat center center;
        background-size: 68% 68%;
        filter: brightness(0) saturate(100%) invert(77%) sepia(6%) saturate(438%) hue-rotate(169deg) brightness(89%) contrast(86%);
    }

    .menu-record {
        right: 40px;
        background: url('@/assets/img/icon/history.svg') no-repeat center center;
        background-size: 80% 80%;
        filter: brightness(0) saturate(100%) invert(77%) sepia(6%) saturate(438%) hue-rotate(169deg) brightness(89%) contrast(86%);
    }

    .nav-title {
        font-size: 30px;
        font-weight: 450;
        font-weight: bolder;
    }
}

.invite-state {
    position: relative;
    left: 50%;
    width: 100%;
    height: 300px;
    background-color: #2c3031;
    transform: translate(-50%, 0);
    border-radius: 30px;
    top: 15px;

    font-size: 32px;
    font-weight: 600;
    font-family: Arial, Helvetica, sans-serif;
    color: #fff;
    img {
        // position: absolute;
        border-radius: 30px;
        height: 100%;
        width: 100%;
    }

    .invite-state-title {
        position: absolute;
        top: 12%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .invite-state-time {
        position: absolute;
        display: flex;
        align-items: center;
        text-align: center;
        padding: 2px 22px;
        padding-top: 6px;
        height: 58px;
        top: 30%;
        left: 50%;
        border-radius: 10px;
        background: linear-gradient(90deg, #2dee88, #96e974);
        transform: translate(-50%, -50%);
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue';
        color: #202222;
        font-size: 16px;
        font-weight: 300;
        :deep(span) {
            font-size: 44px;
            font-weight: 600;
        }
    }

    .invite-state-award {
        position: absolute;
        top: 55%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #25d276;
        font-size: 70px;
        font-weight: bolder;
        overflow-y: hidden;
        line-height: 60px;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
            sans-serif;
    }

    .invite-state-btn {
        position: absolute;
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        top: 82%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: #2dee88 1px solid;
        border-radius: 9999px;
        width: 70%;
        height: 70px;
        color: #2dee88;
        font-weight: 600;

        button {
            width: 90%;
            height: 100%;
        }
    }
}

.invite-turntable {
    top: 70px;
    position: relative;
    left: 50%;
    height: 800px;
    transform: translate(-50%, 0);
}
</style>
